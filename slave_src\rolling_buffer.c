#include "rolling_buffer.h"
#include <stdio.h>

static rb_node node_pool[MAX_NODES];
static int head = NULL_INDEX;
static int tail = NULL_INDEX;
static int count = 0;

void rb_init(void)
{
    for (int i = 0; i < MAX_NODES; i++)
    {
        node_pool[i].used = false;
        node_pool[i].next = NULL_INDEX;
        node_pool[i].prev = NULL_INDEX;
    }
    head = tail = NULL_INDEX;
    count = 0;
}

int alloc_node(void)
{
    for (int i = 0; i < MAX_NODES; i++)
    {
        if (!node_pool[i].used)
        {
            node_pool[i].used = true;
            node_pool[i].next = NULL_INDEX;
            node_pool[i].prev = NULL_INDEX;
            return i;
        }
    }
    return NULL_INDEX; // 没有可用空间
}

void rb_remove_head(void)
{
    if (head == NULL_INDEX)
        return;

    int old_head = head;
    head = node_pool[old_head].next;

    if (head != NULL_INDEX)
    {
        node_pool[head].prev = NULL_INDEX;
    }
    else
    {
        tail = NULL_INDEX;
    }

    node_pool[old_head].used = false;
    node_pool[old_head].next = NULL_INDEX;
    node_pool[old_head].prev = NULL_INDEX;
    count--;
}

void rb_append(sensor_data_t *data)
{
    if (count >= MAX_NODES)
    {
        rb_remove_head(); // 滚动：满了就删除最早的
    }

    int new_index = alloc_node();
    if (new_index == NULL_INDEX)
    {
        // 理论上不该发生
        return;
    }

    node_pool[new_index].data = *data;
    node_pool[new_index].prev = tail;
    node_pool[new_index].next = NULL_INDEX;

    if (tail != NULL_INDEX)
    {
        node_pool[tail].next = new_index;
    }
    tail = new_index;

    if (head == NULL_INDEX)
    {
        head = new_index;
    }

    count++;
}

void rb_print(void)
{
    int idx = head;
    printf("Buffer contents (%d): ", count);
    while (idx != NULL_INDEX)
    {
        printf("T:%.1f P:%.1f ",
               node_pool[idx].data.temperature / 10.0,
               node_pool[idx].data.pressure / 10.0);
        idx = node_pool[idx].next;
    }
    printf("\n");
}

int rb_count(void)
{
    return count;
}

void rb_clear(void)
{
    for (int i = 0; i < MAX_NODES; i++)
    {
        node_pool[i].used = false;
        node_pool[i].next = NULL_INDEX;
        node_pool[i].prev = NULL_INDEX;
    }
    head = tail = NULL_INDEX;
    count = 0;
}

int rb_get_latest_n(sensor_data_t *output, int n)
{
    if (output == NULL || n <= 0)
    {
        return 0;
    }

    int actual_count = (n > count) ? count : n;
    if (actual_count == 0)
    {
        return 0;
    }

    // 从尾部开始向前取数据（最新的数据在尾部）
    int idx = tail;
    int copied = 0;

    // 先收集索引，然后按正确顺序复制数据
    int indices[MAX_NODES];
    int temp_count = 0;

    while (idx != NULL_INDEX && temp_count < actual_count)
    {
        indices[temp_count] = idx;
        idx = node_pool[idx].prev;
        temp_count++;
    }

    // 按从旧到新的顺序复制数据
    for (int i = temp_count - 1; i >= 0; i--)
    {
        output[copied] = node_pool[indices[i]].data;
        copied++;
    }

    return copied;
}

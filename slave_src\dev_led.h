/*
 * Device LED Control Header
 * LED GPIO control and blink functionality
 */

#pragma once

#include "esp_err.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#ifdef __cplusplus
extern "C" {
#endif


/* LED状态定义 */
typedef enum {
    LED_STATE_OFF = 0,    /* LED关闭 */
    LED_STATE_ON = 1      /* LED开启 */
} led_state_t;

/**
 * @brief 初始化LED GPIO
 *
 * 配置LED_GPIO为输出模式，并设置初始状态为关闭
 *
 * @return
 *     - ESP_OK: 初始化成功
 *     - 其他: 初始化失败的错误码
 */
esp_err_t dev_led_init(void);

/**
 * @brief 设置LED状态
 *
 * @param state LED状态 (LED_STATE_ON 或 LED_STATE_OFF)
 * @return
 *     - ESP_OK: 设置成功
 *     - ESP_ERR_INVALID_ARG: 无效参数
 */
esp_err_t dev_led_set_state(led_state_t state);

/**
 * @brief 获取当前LED状态
 *
 * @return led_state_t 当前LED状态
 */
led_state_t dev_led_get_state(void);

/**
 * @brief 切换LED状态
 *
 * 如果LED当前是开启状态，则关闭；如果是关闭状态，则开启
 *
 * @return
 *     - ESP_OK: 切换成功
 */
esp_err_t dev_led_toggle(void);

/**
 * @brief 创建LED闪烁任务
 *
 * 创建一个FreeRTOS任务，让LED以指定间隔闪烁
 *
 * @param blink_interval_ms 闪烁间隔时间（毫秒）
 * @param task_priority 任务优先级
 * @return
 *     - ESP_OK: 任务创建成功
 *     - ESP_FAIL: 任务创建失败
 */
esp_err_t dev_led_start_blink_task(uint32_t blink_interval_ms, uint8_t task_priority);

/**
 * @brief 停止LED闪烁任务
 *
 * @return
 *     - ESP_OK: 任务停止成功
 *     - ESP_FAIL: 任务停止失败
 */
esp_err_t dev_led_stop_blink_task(void);

#ifdef __cplusplus
}
#endif
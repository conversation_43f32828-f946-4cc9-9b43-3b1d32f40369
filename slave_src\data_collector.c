#include "data_collector.h"
#include "rolling_buffer.h"
#include "ble_gatt.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "DATA_COLLECTOR";

// 全局状态变量
static data_collector_state_t collector_state = DATA_COLLECTOR_STOPPED;
static uint8_t current_frequency = 0;
static esp_timer_handle_t sampling_timer = NULL;

// 性能统计
static uint32_t total_samples = 0;
static uint32_t failed_samples = 0;

// 定时器回调函数
static void sampling_timer_callback(void *arg)
{
    sensor_data_t data;
    esp_err_t ret;
    bool sample_failed = false;

    total_samples++;

    // 采集温度数据
    ret = get_temperature(&data.temperature);
    if (ret != ESP_OK)
    {
        data.temperature = 0;
        sample_failed = true;
    }

    // 采集压力数据
    ret = get_pressure(&data.pressure);
    if (ret != ESP_OK)
    {
        data.pressure = 0;
        sample_failed = true;
    }

    if (sample_failed)
    {
        failed_samples++;
    }

    // 添加到滚动缓冲区
    rb_append(&data);

    // 实测高频日志输出很吃资源,严重影响性能

    // 每500次采集输出一次统计信息（仅在高频时）(仅做调试用)
    if (current_frequency > 10 && (total_samples % 500) == 0)
    {
        ESP_LOGI(TAG, "采集统计: 总计%lu次, 失败%lu次, 成功率%.1f%%",
                 total_samples, failed_samples,
                 (total_samples - failed_samples) * 100.0 / total_samples);
    }
}

esp_err_t data_collector_init(void)
{
    ESP_LOGI(TAG, "初始化数据采集器");

    // 初始化滚动缓冲区
    rb_init();

    collector_state = DATA_COLLECTOR_STOPPED;
    current_frequency = 0;

    ESP_LOGI(TAG, "数据采集器初始化完成");
    return ESP_OK;
}

esp_err_t data_collector_deinit(void)
{
    ESP_LOGI(TAG, "反初始化数据采集器");

    // 停止定时器
    data_collector_stop();

    // 清空缓冲区
    rb_clear();

    collector_state = DATA_COLLECTOR_STOPPED;
    current_frequency = 0;

    ESP_LOGI(TAG, "数据采集器反初始化完成");
    return ESP_OK;
}

esp_err_t data_collector_start(uint8_t frequency)
{
    if (frequency < 1 || frequency > 20)
    {
        ESP_LOGE(TAG, "无效的采集频率: %d (有效范围: 1-20)", frequency);
        return ESP_ERR_INVALID_ARG;
    }

    // 高频采集警告
    if (frequency > 10)
    {
        ESP_LOGW(TAG, "高频采集 %d Hz 可能影响BLE通信性能", frequency);
    }

    // 如果已经在运行，先停止
    if (collector_state == DATA_COLLECTOR_RUNNING)
    {
        data_collector_stop();
    }

    ESP_LOGI(TAG, "启动定时采集，频率: %d Hz", frequency);

    // 计算定时器周期（微秒）
    uint64_t period_us = 1000000 / frequency;

    // 创建定时器
    esp_timer_create_args_t timer_args = {
        .callback = sampling_timer_callback,
        .arg = NULL,
        .name = "sampling_timer"};

    esp_err_t ret = esp_timer_create(&timer_args, &sampling_timer);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "创建定时器失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 启动定时器
    ret = esp_timer_start_periodic(sampling_timer, period_us);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "启动定时器失败: %s", esp_err_to_name(ret));
        esp_timer_delete(sampling_timer);
        sampling_timer = NULL;
        return ret;
    }

    collector_state = DATA_COLLECTOR_RUNNING;
    current_frequency = frequency;

    ESP_LOGI(TAG, "定时采集已启动，周期: %llu us", period_us);
    return ESP_OK;
}

esp_err_t data_collector_stop(void)
{
    if (collector_state == DATA_COLLECTOR_STOPPED)
    {
        return ESP_OK; // 已经停止
    }

    ESP_LOGI(TAG, "停止定时采集");

    if (sampling_timer != NULL)
    {
        esp_timer_stop(sampling_timer);
        esp_timer_delete(sampling_timer);
        sampling_timer = NULL;
    }

    collector_state = DATA_COLLECTOR_STOPPED;
    current_frequency = 0;

    ESP_LOGI(TAG, "定时采集已停止");
    return ESP_OK;
}

data_collector_state_t data_collector_get_state(void)
{
    return collector_state;
}

uint8_t data_collector_get_frequency(void)
{
    return current_frequency;
}

esp_err_t data_collector_sample_once(sensor_data_t *data)
{
    if (data == NULL)
    {
        return ESP_ERR_INVALID_ARG;
    }

    esp_err_t ret;

    // 采集温度数据
    ret = get_temperature(&data->temperature);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "温度采集失败: %s", esp_err_to_name(ret));
        data->temperature = 0;
    }

    // 采集压力数据
    ret = get_pressure(&data->pressure);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "压力采集失败: %s", esp_err_to_name(ret));
        data->pressure = 0;
    }

    // 添加时间戳
    // data->timestamp = esp_timer_get_time() / 1000; // 转换为毫秒

    ESP_LOGD(TAG, "单次采集: T=%.1f°C, P=%.1fkPa", data->temperature / 10.0, data->pressure / 10.0);

    return ESP_OK;
}

int data_collector_get_latest_data(sensor_data_t *output, int n)
{
    return rb_get_latest_n(output, n);
}

void data_collector_clear_buffer(void)
{
    rb_clear();
}

int data_collector_get_buffer_count(void)
{
    return rb_count();
}

void data_collector_get_stats(uint32_t *total_samples_out, uint32_t *failed_samples_out)
{
    if (total_samples_out)
        *total_samples_out = total_samples;
    if (failed_samples_out)
        *failed_samples_out = failed_samples;
}

void data_collector_reset_stats(void)
{
    total_samples = 0;
    failed_samples = 0;
    ESP_LOGI(TAG, "性能统计已重置");
}

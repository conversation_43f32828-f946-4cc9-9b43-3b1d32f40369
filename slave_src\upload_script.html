<!DOCTYPE html>
<html>
<head>
    <title>TPM Jar Management</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        input[type="text"], input[type="file"] { width: 100%; padding: 5px; box-sizing: border-box; }
        button {
            padding: 10px 20px;
            margin: 2px;
            cursor: pointer;
            min-width: 100px;
            height: 40px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .section { margin-bottom: 30px; border: 1px solid #ccc; padding: 15px; }
        .section h3 { margin-top: 0; }
        .warning { color: red; font-weight: bold; }
        .info { background-color: #e7f3ff; padding: 10px; border-left: 4px solid #2196F3; }
        .lang-switch { float: right; margin-bottom: 20px; }
        .lang-switch button {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            min-width: 80px;
            height: 35px;
            font-size: 13px;
        }
        .lang-switch button.active { background-color: #4CAF50; color: white; }
        .header { overflow: hidden; margin-bottom: 20px; }
        .btn-danger {
            background-color: #ff6b6b !important;
            color: white !important;
            font-weight: bold !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="lang-switch">
            <button onclick="switchLanguage('zh')" id="lang-zh" class="active">中文简体</button>
            <button onclick="switchLanguage('en')" id="lang-en" >English</button>
        </div>
        <h1 data-lang-en="TPM Jar Management" data-lang-zh="TPM 罐体控制器管理">TPM 罐体控制器管理</h1>
    </div>

    <!-- Device ID Management Section -->
    <div class="section">
        <h3 data-lang-en="Device ID Management" data-lang-zh="设备ID管理">设备ID管理</h3>
        <table>
            <tr>
                <th width="200px" data-lang-en="Current Device ID" data-lang-zh="当前设备ID">当前设备ID</th>
                <td id="currentDeviceId" data-lang-en="Loading..." data-lang-zh="加载中...">加载中...</td>
                <td width="120px"><button onclick="getCurrentId()" data-lang-en="Refresh" data-lang-zh="刷新">刷新</button></td>
            </tr>
            <tr>
                <th data-lang-en="Set New Device ID" data-lang-zh="设置新设备ID">设置新设备ID</th>
                <td><input type="text" id="newDeviceId" data-lang-en="Enter ID (1-255)" data-lang-zh="输入ID (1-255)" placeholder="输入ID (1-255)" maxlength="3"></td>
                <td width="120px"><button onclick="setDeviceId()" data-lang-en="Set ID" data-lang-zh="设置ID">设置ID</button></td>
            </tr>
        </table>
    </div>

    <!-- Firmware Update Section -->
    <div class="section">
        <h3 data-lang-en="Firmware Update (OTA)" data-lang-zh="固件更新 (OTA)">固件更新 (OTA)</h3>
        <div class="warning" data-lang-en="Warning: Only upload valid ESP32 firmware (.bin) files!" data-lang-zh="警告：只能上传有效的ESP32固件(.bin)文件！">警告：只能上传有效的ESP32固件(.bin)文件！</div>
        <table>
            <tr>
                <th width="200px" data-lang-en="Select Firmware" data-lang-zh="选择固件">选择固件</th>
                <td><input type="file" id="firmwarefile" accept=".bin"></td>
            </tr>
            <tr>
                <th data-lang-en="Action" data-lang-zh="操作">操作</th>
                <td colspan="2">
                    <button id="otaupload" class="btn-danger" onclick="uploadFirmware()" data-lang-en="Update Firmware" data-lang-zh="更新固件">
                        更新固件
                    </button>
                </td>
            </tr>
        </table>
        <div class="info" data-lang-en="Max firmware size: 2MB. Device will restart after update." data-lang-zh="最大固件大小：2MB。更新后设备将重启。">最大固件大小：2MB。更新后设备将重启。</div>
    </div>

    <!-- File Upload Section -->
    <div class="section">
        <h3 data-lang-en="File Upload" data-lang-zh="文件上传">文件上传</h3>
        <table>
            <tr>
                <th width="200px" data-lang-en="Select File" data-lang-zh="选择文件">选择文件</th>
                <td><input type="file" id="newfile" onchange="setpath()"></td>
            </tr>
            <tr>
                <th data-lang-en="Server Path" data-lang-zh="服务器路径">服务器路径</th>
                <td><input type="text" id="filepath" data-lang-en="File path on server" data-lang-zh="服务器上的文件路径" placeholder="服务器上的文件路径"></td>
                <td width="120px"><button id="upload" onclick="upload()" data-lang-en="Upload" data-lang-zh="上传">上传</button></td>
            </tr>
        </table>
        <div class="info" data-lang-en="Max file size: 512KB" data-lang-zh="最大文件大小：512KB">最大文件大小：512KB</div>
    </div>
    <script>
        // Language management
        let currentLanguage = 'zh';

        // Language texts
        const messages = {
            en: {
                'loading': 'Loading...',
                'error_loading_id': 'Error loading ID',
                'enter_device_id': 'Please enter a device ID!',
                'invalid_device_id': 'Device ID must be a number between 1 and 255!',
                'confirm_change_id': 'Are you sure you want to change the device ID to',
                'id_updated_success': 'Device ID updated successfully!',
                'id_update_failed': 'Failed to update device ID:',
                'no_file_selected': 'No file selected!',
                'no_path_set': 'File path on server is not set!',
                'no_spaces_allowed': 'File path on server cannot have spaces!',
                'no_filename': 'File name not specified after path!',
                'file_too_large': 'File size must be less than 512KB!',
                'file_uploaded': 'File uploaded successfully!',
                'connection_closed': 'Server closed the connection abruptly!',
                'no_firmware_selected': 'No firmware file selected!',
                'invalid_firmware': 'Please select a valid firmware file with .bin extension!',
                'firmware_too_large': 'Firmware file size must be less than 2MB!',
                'confirm_firmware_update': 'Are you sure you want to update the firmware?\\nThis will restart the device!',
                'firmware_success': 'Firmware update successful! Device will restart.',
                'device_restarting': 'Device is restarting after firmware update...',
                'firmware_failed': 'Firmware update failed:',
                'updating': 'Updating...',
                'uploading': 'Uploading...'
            },
            zh: {
                'loading': '加载中...',
                'error_loading_id': '加载ID错误',
                'enter_device_id': '请输入设备ID！',
                'invalid_device_id': '设备ID必须是1到255之间的数字！',
                'confirm_change_id': '确定要将设备ID更改为',
                'id_updated_success': '设备ID更新成功！',
                'id_update_failed': '设备ID更新失败：',
                'no_file_selected': '未选择文件！',
                'no_path_set': '未设置服务器文件路径！',
                'no_spaces_allowed': '服务器文件路径不能包含空格！',
                'no_filename': '路径后未指定文件名！',
                'file_too_large': '文件大小必须小于512KB！',
                'file_uploaded': '文件上传成功！',
                'connection_closed': '服务器突然关闭连接！',
                'no_firmware_selected': '未选择固件文件！',
                'invalid_firmware': '请选择有效的.bin固件文件！',
                'firmware_too_large': '固件文件大小必须小于2MB！',
                'confirm_firmware_update': '确定要更新固件吗？\\n这将重启设备！',
                'firmware_success': '固件更新成功！设备将重启。',
                'device_restarting': '固件更新后设备正在重启...',
                'firmware_failed': '固件更新失败：',
                'updating': '更新中...',
                'uploading': '上传中...'
            }
        };

        function switchLanguage(lang) {
            currentLanguage = lang;

            // Update button states
            document.getElementById('lang-en').classList.toggle('active', lang === 'en');
            document.getElementById('lang-zh').classList.toggle('active', lang === 'zh');

            // Update all elements with language attributes
            const elements = document.querySelectorAll('[data-lang-en], [data-lang-zh]');
            elements.forEach(element => {
                const text = element.getAttribute('data-lang-' + lang);
                if (text) {
                    if (element.tagName === 'INPUT' && element.type === 'text') {
                        element.placeholder = text;
                    } else {
                        element.textContent = text;
                    }
                }
            });

            // Update current device ID display if it shows loading or error
            const currentIdElement = document.getElementById('currentDeviceId');
            if (currentIdElement.textContent === 'Loading...' || currentIdElement.textContent === '加载中...') {
                currentIdElement.textContent = messages[lang]['loading'];
            } else if (currentIdElement.textContent === 'Error loading ID' || currentIdElement.textContent === '加载ID错误') {
                currentIdElement.textContent = messages[lang]['error_loading_id'];
            }
        }

        function getMessage(key) {
            return messages[currentLanguage][key] || messages['en'][key];
        }

        // Load device ID when page loads
        window.onload = function() {
            switchLanguage('zh');
            getCurrentId();
        };

        // Device ID Management Functions
        function getCurrentId() {
            var xhttp = new XMLHttpRequest();
            xhttp.onreadystatechange = function() {
                if (xhttp.readyState == 4) {
                    if (xhttp.status == 200) {
                        try {
                            var response = JSON.parse(xhttp.responseText);
                            document.getElementById("currentDeviceId").innerHTML = response.device_id;
                        } catch (e) {
                            document.getElementById("currentDeviceId").innerHTML = xhttp.responseText;
                        }
                    } else {
                        document.getElementById("currentDeviceId").innerHTML = getMessage('error_loading_id');
                    }
                }
            };
            xhttp.open("GET", "/api/device_id", true);
            xhttp.send();
        }

        function setDeviceId() {
            var deviceId = document.getElementById("newDeviceId").value.trim();

            if (deviceId === "") {
                alert(getMessage('enter_device_id'));
                return;
            }

            var id = parseInt(deviceId);
            if (isNaN(id) || id < 1 || id > 255) {
                alert(getMessage('invalid_device_id'));
                return;
            }

            if (!confirm(getMessage('confirm_change_id') + " " + id + "?")) {
                return;
            }

            var xhttp = new XMLHttpRequest();
            xhttp.onreadystatechange = function() {
                if (xhttp.readyState == 4) {
                    if (xhttp.status == 200) {
                        alert(getMessage('id_updated_success'));
                        document.getElementById("newDeviceId").value = "";
                        getCurrentId();
                    } else {
                        alert(getMessage('id_update_failed') + " " + xhttp.responseText);
                    }
                }
            };
            xhttp.open("POST", "/api/device_id", true);
            xhttp.setRequestHeader("Content-Type", "application/json");
            xhttp.send(JSON.stringify({device_id: id}));
        }

        // File Upload Functions
        function setpath() {
            var default_path = document.getElementById("newfile").files[0].name;
            document.getElementById("filepath").value = default_path;
        }

        function upload() {
            var filePath = document.getElementById("filepath").value;
            var upload_path = "/upload/" + filePath;
            var fileInput = document.getElementById("newfile").files;

            var MAX_FILE_SIZE = 512*1024;

            if (fileInput.length == 0) {
                alert(getMessage('no_file_selected'));
            } else if (filePath.length == 0) {
                alert(getMessage('no_path_set'));
            } else if (filePath.indexOf(' ') >= 0) {
                alert(getMessage('no_spaces_allowed'));
            } else if (filePath[filePath.length-1] == '/') {
                alert(getMessage('no_filename'));
            } else if (fileInput[0].size > MAX_FILE_SIZE) {
                alert(getMessage('file_too_large'));
            } else {
                document.getElementById("newfile").disabled = true;
                document.getElementById("filepath").disabled = true;
                document.getElementById("upload").disabled = true;
                document.getElementById("upload").innerHTML = getMessage('uploading');

                var file = fileInput[0];
                var xhttp = new XMLHttpRequest();
                xhttp.onreadystatechange = function() {
                    if (xhttp.readyState == 4) {
                        var uploadBtn = document.getElementById("upload");
                        var uploadText = uploadBtn.getAttribute('data-lang-' + currentLanguage) || 'Upload';

                        if (xhttp.status == 200) {
                            alert(getMessage('file_uploaded'));
                            document.getElementById("newfile").disabled = false;
                            document.getElementById("filepath").disabled = false;
                            document.getElementById("upload").disabled = false;
                            uploadBtn.innerHTML = uploadText;
                            document.getElementById("newfile").value = "";
                            document.getElementById("filepath").value = "";
                        } else if (xhttp.status == 0) {
                            alert(getMessage('connection_closed'));
                            location.reload()
                        } else {
                            alert(xhttp.status + " Error!\n" + xhttp.responseText);
                            document.getElementById("newfile").disabled = false;
                            document.getElementById("filepath").disabled = false;
                            document.getElementById("upload").disabled = false;
                            uploadBtn.innerHTML = uploadText;
                        }
                    }
                };
                xhttp.open("POST", upload_path, true);
                xhttp.send(file);
            }
        }

        // Firmware Update Functions
        function uploadFirmware() {
            var fileInput = document.getElementById("firmwarefile").files;

            if (fileInput.length == 0) {
                alert(getMessage('no_firmware_selected'));
                return;
            }

            var file = fileInput[0];

            // Check if file has .bin extension
            if (!file.name.toLowerCase().endsWith('.bin')) {
                alert(getMessage('invalid_firmware'));
                return;
            }

            // Check firmware file size (2MB limit)
            if (file.size > 2*1024*1024) {
                alert(getMessage('firmware_too_large'));
                return;
            }

            // Confirm firmware update
            if (!confirm(getMessage('confirm_firmware_update'))) {
                return;
            }

            document.getElementById("firmwarefile").disabled = true;
            document.getElementById("otaupload").disabled = true;
            document.getElementById("otaupload").innerHTML = getMessage('updating');

            var xhttp = new XMLHttpRequest();
            xhttp.onreadystatechange = function() {
                if (xhttp.readyState == 4) {
                    var otaBtn = document.getElementById("otaupload");
                    var otaText = otaBtn.getAttribute('data-lang-' + currentLanguage) || 'Update Firmware';

                    if (xhttp.status == 200) {
                        alert(getMessage('firmware_success'));
                        setTimeout(function() {
                            location.reload();
                        }, 5000);
                    } else if (xhttp.status == 0) {
                        alert(getMessage('device_restarting'));
                        setTimeout(function() {
                            location.reload();
                        }, 10000);
                    } else {
                        alert(getMessage('firmware_failed') + " " + xhttp.status + " - " + xhttp.responseText);
                        document.getElementById("firmwarefile").disabled = false;
                        document.getElementById("otaupload").disabled = false;
                        otaBtn.innerHTML = otaText;
                    }
                }
            };

            xhttp.open("POST", "/ota", true);
            xhttp.send(file);
        }
    </script>
</body>
</html>

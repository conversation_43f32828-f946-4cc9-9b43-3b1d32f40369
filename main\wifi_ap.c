/*
 * WiFi AP Mode Configuration for ESP32 File Server with OTA
 */

#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_netif.h"
#include "esp_mac.h"
#include "esp_wifi_types.h"
#include "esp_netif_ip_addr.h"

#include "lwip/err.h"
#include "lwip/sys.h"
#include "lwip/ip4_addr.h"

/* WiFi AP configuration - 优化BLE共存，固定信道避免干扰 */
#define WIFI_AP_SSID "TPM-Jar0"
#define WIFI_AP_PASS ""              // 无密码 - 开放网络
#define WIFI_AP_CHANNEL 13           // 使用信道13，与BLE频段干扰最小
#define WIFI_AP_MAX_CONN 2           // 标准最大连接数
#define WIFI_AP_BEACON_INTERVAL 1000 // 标准beacon间隔以确保可发现性 (默认100ms)
#define WIFI_AP_DTIM_PERIOD 3        // 设置DTIM周期以优化功耗

/*
 * WiFi 2.4GHz信道与BLE干扰分析：
 * BLE使用2402-2480MHz，分为40个信道，每个信道间隔2MHz
 * WiFi 2.4GHz信道：
 * - 信道1: 2412MHz (与BLE信道5重叠，干扰中等)
 * - 信道6: 2437MHz (与BLE信道18重叠，干扰较大)
 * - 信道11: 2462MHz (与BLE信道35重叠，干扰较大)
 * - 信道13: 2472MHz (与BLE信道37重叠，但BLE信道37是广播信道，可优化)
 *
 * 选择信道13的原因：
 * 1. 远离WiFi常用信道1,6,11，减少WiFi设备间干扰
 * 2. 虽然与BLE广播信道37重叠，但可以通过BLE参数优化
 * 3. 在中国地区合法使用
 */

static const char *TAG = "wifi_ap";

/* Global variables for WiFi management */
static esp_netif_t *wifi_netif = NULL;
static esp_event_handler_instance_t instance_any_id = NULL;
static bool wifi_initialized = false;

/* WiFi稳定性监控变量 */
static uint32_t wifi_disconnect_count = 0;
static uint32_t wifi_connect_count = 0;
static uint32_t last_disconnect_time = 0;

static void wifi_event_handler(void *arg, esp_event_base_t event_base, int32_t event_id, void *event_data)
{
    if (event_base == WIFI_EVENT)
    {
        switch (event_id)
        {
        case WIFI_EVENT_AP_STACONNECTED:
        {
            wifi_event_ap_staconnected_t *event = (wifi_event_ap_staconnected_t *)event_data;
            wifi_connect_count++;
            ESP_LOGI(TAG, "Station connected! MAC: %02x:%02x:%02x:%02x:%02x:%02x, AID=%d (Total: %lu)",
                     event->mac[0], event->mac[1], event->mac[2],
                     event->mac[3], event->mac[4], event->mac[5], event->aid, wifi_connect_count);
            break;
        }
        case WIFI_EVENT_AP_STADISCONNECTED:
        {
            wifi_event_ap_stadisconnected_t *event = (wifi_event_ap_stadisconnected_t *)event_data;
            wifi_disconnect_count++;
            uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
            uint32_t time_since_last = current_time - last_disconnect_time;
            last_disconnect_time = current_time;

            ESP_LOGI(TAG, "Station disconnected! MAC: %02x:%02x:%02x:%02x:%02x:%02x, AID=%d, Reason: %d",
                     event->mac[0], event->mac[1], event->mac[2],
                     event->mac[3], event->mac[4], event->mac[5], event->aid, event->reason);
            ESP_LOGI(TAG, "Disconnect count: %lu, Time since last: %lu ms", wifi_disconnect_count, time_since_last);

            // 如果断开过于频繁，记录警告
            if (time_since_last < 30000 && wifi_disconnect_count > 3)
            { // 30秒内断开超过3次
                ESP_LOGW(TAG, "Frequent disconnections detected! Consider checking interference.");
            }
            break;
        }
        case WIFI_EVENT_AP_START:
            ESP_LOGI(TAG, "WiFi AP started successfully");
            break;
        case WIFI_EVENT_AP_STOP:
            ESP_LOGI(TAG, "WiFi AP stopped");
            break;
        default:
            ESP_LOGI(TAG, "WiFi event: %ld", event_id);
            break;
        }
    }
    else if (event_base == IP_EVENT)
    {
        switch (event_id)
        {
        case IP_EVENT_AP_STAIPASSIGNED:
        {
            ip_event_ap_staipassigned_t *event = (ip_event_ap_staipassigned_t *)event_data;
            ESP_LOGI(TAG, "Station assigned IP: " IPSTR, IP2STR(&event->ip));
            break;
        }
        default:
            ESP_LOGI(TAG, "IP event: %ld", event_id);
            break;
        }
    }
}

//
esp_err_t wifi_init_ap(void)
{
    esp_err_t ret;

    ESP_LOGI(TAG, "Starting simple WiFi AP initialization...");

    // 初始化网络接口
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // 创建默认WiFi AP网络接口
    esp_netif_t *netif = esp_netif_create_default_wifi_ap();

    // 配置AP的IP地址
    esp_netif_ip_info_t ip_info;
    IP4_ADDR(&ip_info.ip, 192, 168, 4, 1);        // AP IP地址
    IP4_ADDR(&ip_info.gw, 192, 168, 4, 1);        // 网关地址
    IP4_ADDR(&ip_info.netmask, 255, 255, 255, 0); // 子网掩码

    esp_err_t ret_ip = esp_netif_set_ip_info(netif, &ip_info);
    if (ret_ip == ESP_OK)
    {
        ESP_LOGI(TAG, "AP IP configured: ***********");
    }
    else
    {
        ESP_LOGW(TAG, "Failed to set AP IP: %s", esp_err_to_name(ret_ip));
    }

    // 启动DHCP服务器
    ret_ip = esp_netif_dhcps_start(netif);
    if (ret_ip == ESP_OK)
    {
        ESP_LOGI(TAG, "DHCP server started");
    }
    else if (ret_ip == ESP_ERR_ESP_NETIF_DHCP_ALREADY_STARTED)
    {
        ESP_LOGI(TAG, "DHCP server already started");
    }
    else
    {
        ESP_LOGW(TAG, "Failed to start DHCP server: %s", esp_err_to_name(ret_ip));
    }

    // 注册WiFi事件处理器
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL));

    // 注册IP事件处理器
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL));

    // 使用默认WiFi配置
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    // 完整的WiFi AP配置 - 固定信道13避免BLE干扰
    wifi_config_t wifi_config = {
        .ap = {
            .ssid = WIFI_AP_SSID,
            .ssid_len = strlen(WIFI_AP_SSID),
            .channel = WIFI_AP_CHANNEL, // 固定信道13，减少与BLE干扰
            .password = "",
            .max_connection = WIFI_AP_MAX_CONN,
            .authmode = WIFI_AUTH_OPEN,
            .ssid_hidden = 0,                           // 确保SSID可见
            .beacon_interval = WIFI_AP_BEACON_INTERVAL, // 标准beacon间隔
            .pairwise_cipher = WIFI_CIPHER_TYPE_NONE,   // 开放网络无加密
            .ftm_responder = false,                     // 禁用FTM
            .pmf_cfg = {
                .capable = false,
                .required = false,
            },
        },
    };

    ESP_LOGI(TAG, "WiFi AP configuration:");
    ESP_LOGI(TAG, "  SSID: %s", wifi_config.ap.ssid);
    ESP_LOGI(TAG, "  Channel: %d", wifi_config.ap.channel);
    ESP_LOGI(TAG, "  Auth mode: %d (0=OPEN)", wifi_config.ap.authmode);
    ESP_LOGI(TAG, "  Max connections: %d", wifi_config.ap.max_connection);

    ESP_LOGI(TAG, "Setting WiFi mode to AP...");
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_AP));

    ESP_LOGI(TAG, "Setting WiFi configuration...");
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_AP, &wifi_config));

    ESP_LOGI(TAG, "Starting WiFi...");
    ret = esp_wifi_start();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to start WiFi: %s", esp_err_to_name(ret));
        return ret;
    }

    // 等待WiFi启动
    vTaskDelay(pdMS_TO_TICKS(2000));

    // 设置WiFi抗干扰参数
    // 设置国家代码确保信道13可用
    wifi_country_t country = {
        .cc = "CN",                           // 中国
        .schan = 1,                           // 起始信道
        .nchan = 13,                          // 信道数量
        .max_tx_power = 84,                   // 最大发射功率 (21dBm)
        .policy = WIFI_COUNTRY_POLICY_MANUAL, // 手动策略，强制使用指定信道
    };
    ret = esp_wifi_set_country(&country);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "Failed to set WiFi country: %s", esp_err_to_name(ret));
    }
    else
    {
        ESP_LOGI(TAG, "WiFi country set to CN, channel 13 enabled");
    }

    // 强制设置带宽为20MHz以减少频谱占用
    ret = esp_wifi_set_bandwidth(WIFI_IF_AP, WIFI_BW_HT20);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "Failed to set WiFi bandwidth: %s", esp_err_to_name(ret));
    }
    else
    {
        ESP_LOGI(TAG, "WiFi bandwidth fixed to 20MHz");
    }

    // 设置WiFi协议，禁用高速模式以减少干扰
    ret = esp_wifi_set_protocol(WIFI_IF_AP, WIFI_PROTOCOL_11B | WIFI_PROTOCOL_11G);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "Failed to set WiFi protocol: %s", esp_err_to_name(ret));
    }
    else
    {
        ESP_LOGI(TAG, "WiFi protocol set to 802.11b/g (no 11n for stability)");
    }

    // 检查WiFi状态
    wifi_mode_t mode;
    ret = esp_wifi_get_mode(&mode);
    if (ret == ESP_OK)
    {
        ESP_LOGI(TAG, "WiFi mode: %d", mode);
    }

    // 获取AP信息
    wifi_config_t get_conf;
    ret = esp_wifi_get_config(WIFI_IF_AP, &get_conf);
    if (ret == ESP_OK)
    {
        ESP_LOGI(TAG, "AP Config - SSID: %s, Channel: %d, Auth: %d",
                 (char *)get_conf.ap.ssid, get_conf.ap.channel, get_conf.ap.authmode);
    }

    ESP_LOGI(TAG, "=== WiFi AP Started with BLE Coexistence ===");
    ESP_LOGI(TAG, "SSID: ESP32-Simple-Test");
    ESP_LOGI(TAG, "Password: None (Open)");
    ESP_LOGI(TAG, "Channel: 13 (2472MHz) - Fixed for BLE coexistence");
    ESP_LOGI(TAG, "Bandwidth: 20MHz (Fixed)");
    ESP_LOGI(TAG, "Protocol: 802.11b/g (Stable mode)");
    ESP_LOGI(TAG, "AP IP: ***********");
    ESP_LOGI(TAG, "DHCP Range: *********** - *************");
    ESP_LOGI(TAG, "BLE Optimizations: Enabled");
    ESP_LOGI(TAG, "Please search for this network on your device");
    ESP_LOGI(TAG, "==========================================");

    return ESP_OK;
}

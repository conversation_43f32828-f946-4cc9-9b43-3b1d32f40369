/*
 * SPDX-FileCopyrightText: 2021-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Unlicense OR CC0-1.0
 */

#include "ble_gap.h"
#include "esp_log.h"
#include "esp_gap_ble_api.h"
#include "esp_bt_defs.h"

#define BLE_GAP_TAG "BLE_GAP"

// GAP 相关静态变量
static uint8_t adv_config_done = 0;

#define CONFIG_SET_RAW_ADV_DATA
#ifdef CONFIG_SET_RAW_ADV_DATA

// 广播报文
static uint8_t raw_adv_data[] = {
    /* Flags - 发现模式 */
    0x02,                 // 长度：2字节
    ESP_BLE_AD_TYPE_FLAG, // 类型：标志位 (0x01)
    0x06,                 // 值：发现模式标志
                          // 0x06 = 0000 0110 (二进制)
                          // Bit 1: LE General Discoverable Mode (可被一般发现)
                          // Bit 2: BR/EDR Not Supported (不支持经典蓝牙)

    /* TX Power Level - 发射功率 */
    0x02,                   // 长度：2字节
    ESP_BLE_AD_TYPE_TX_PWR, // 类型：发射功率 (0x0A)
    0xEB,                   // 值：功率等级 (-21dBm)

    /* Complete 16-bit Service UUIDs - 服务UUID */
    0x03,                       // 长度：3字节
    ESP_BLE_AD_TYPE_16SRV_CMPL, // 类型：完整16位服务UUID (0x03)
    0xFF, 0x00,                 // 值：服务UUID 0x00FF (小端序)

    /* Complete Local Name - 设备名称 */
    0x0A,                                       // 长度：10字节
    ESP_BLE_AD_TYPE_NAME_CMPL,                  // 类型：完整设备名称 (0x09)
    'B', 'L', 'E', '-', 'S', 'L', 'A', 'V', 'E' // 值：设备名称

};

static uint8_t raw_scan_rsp_data[] = {
    /* Flags */
    0x02, ESP_BLE_AD_TYPE_FLAG, 0x06,
    /* TX Power Level */
    0x02, ESP_BLE_AD_TYPE_TX_PWR, 0xEB,
    /* Complete 16-bit Service UUIDs */
    0x03, ESP_BLE_AD_TYPE_16SRV_CMPL, 0xFF, 0x00};

#else
static uint8_t service_uuid[16] = {
    /* LSB <--------------------------------------------------------------------------------> MSB */
    // first uuid, 16bit, [12],[13] is the value
    0xfb,
    0x34,
    0x9b,
    0x5f,
    0x80,
    0x00,
    0x00,
    0x80,
    0x00,
    0x10,
    0x00,
    0x00,
    0xFF,
    0x00,
    0x00,
    0x00,
};

/* The length of adv data must be less than 31 bytes */
static esp_ble_adv_data_t adv_data = {
    .set_scan_rsp = false,
    .include_name = true,
    .include_txpower = true,
    .min_interval = 0x0006, // slave connection min interval, Time = min_interval * 1.25 msec
    .max_interval = 0x0010, // slave connection max interval, Time = max_interval * 1.25 msec
    .appearance = 0x00,
    .manufacturer_len = 0,       // TEST_MANUFACTURER_DATA_LEN,
    .p_manufacturer_data = NULL, // test_manufacturer,
    .service_data_len = 0,
    .p_service_data = NULL,
    .service_uuid_len = sizeof(service_uuid),
    .p_service_uuid = service_uuid,
    .flag = (ESP_BLE_ADV_FLAG_GEN_DISC | ESP_BLE_ADV_FLAG_BREDR_NOT_SPT),
};

// scan response data
static esp_ble_adv_data_t scan_rsp_data = {
    .set_scan_rsp = true,
    .include_name = true,
    .include_txpower = true,
    .min_interval = 0x0006,
    .max_interval = 0x0010,
    .appearance = 0x00,
    .manufacturer_len = 0,       // TEST_MANUFACTURER_DATA_LEN,
    .p_manufacturer_data = NULL, //&test_manufacturer[0],
    .service_data_len = 0,
    .p_service_data = NULL,
    .service_uuid_len = sizeof(service_uuid),
    .p_service_uuid = service_uuid,
    .flag = (ESP_BLE_ADV_FLAG_GEN_DISC | ESP_BLE_ADV_FLAG_BREDR_NOT_SPT),
};
#endif /* CONFIG_SET_RAW_ADV_DATA */

static esp_ble_adv_params_t adv_params = {
    .adv_int_min = 0x20,
    .adv_int_max = 0x40,
    .adv_type = ADV_TYPE_IND,
    .own_addr_type = BLE_ADDR_TYPE_PUBLIC,
    .channel_map = ADV_CHNL_ALL,
    .adv_filter_policy = ADV_FILTER_ALLOW_SCAN_ANY_CON_ANY,
};

// 用于处理 设备连接、广播、扫描、安全性 等相关的 协议栈事件
// 注意: 连接相关的事件（如连接成功、断开连接等）是在 GATT 层处理的，不是 GAP 层。
// 该函数注册成功后, 此时的从机状态为
// GAP层正常工作
// 开始对外广播设备信息
// 等待主机扫描和连接,
// 可以响应主机的扫描请求
// 下一步则会发生
// 1. 从机持续广播
// 2. 主机扫描发现从机
// 3. 主机发起连接请求  注意, GAP层建立连接后,协议栈会自动处理该事件,不需要开发者手动处理的
// 4. 此时会触发GATT层的连接事件(ESP_GATTS_CONNECT_EVT), 所以在此事件中处理连接事件即可
// 5. 开始GATT服务交互
void gap_event_handler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t *param)
{
    switch (event)
    {
    // 广播内容设置完成
    // esp_ble_gap_config_adv_data_raw()后触发该事件
    case ESP_GAP_BLE_ADV_DATA_RAW_SET_COMPLETE_EVT:
        adv_config_done &= (~ADV_CONFIG_FLAG);
        if (adv_config_done == 0)
        {
            // 注意这里是广播参数, 不是广播数据
            esp_ble_gap_start_advertising(&adv_params);
        }
        break;

    // 扫描响应内容设置完成
    // esp_ble_gap_config_scan_rsp_data_raw()后触发该事件
    // 为什么会调用两次？
    // 第一次调用可能因为扫描响应数据还没设置好而不会真正启动
    // 第二次调用时，所有数据都准备好了，真正启动广播
    // 但每次调用都会触发ESP_GAP_BLE_ADV_START_COMPLETE_EVT事件
    case ESP_GAP_BLE_SCAN_RSP_DATA_RAW_SET_COMPLETE_EVT:
        adv_config_done &= (~SCAN_RSP_CONFIG_FLAG);
        if (adv_config_done == 0)
        {
            esp_ble_gap_start_advertising(&adv_params);
        }
        break;

    // 广播启动
    // esp_ble_gap_start_advertising()后触发该事件
    case ESP_GAP_BLE_ADV_START_COMPLETE_EVT:
        /* advertising start complete event to indicate advertising start successfully or failed */
        if (param->adv_start_cmpl.status != ESP_BT_STATUS_SUCCESS)
        {
            ESP_LOGE(BLE_GAP_TAG, "advertising start failed");
        }
        else
        {
            ESP_LOGI(BLE_GAP_TAG, "advertising start successfully");
        }
        break;
    // 广播停止
    case ESP_GAP_BLE_ADV_STOP_COMPLETE_EVT:
        if (param->adv_stop_cmpl.status != ESP_BT_STATUS_SUCCESS)
        {
            ESP_LOGE(BLE_GAP_TAG, "Advertising stop failed");
        }
        else
        {
            ESP_LOGI(BLE_GAP_TAG, "Stop adv successfully");
        }
        break;
    // BLE 为了节省功耗或优化通信性能，在建立连接后可能会动态调整这些连接参数,完成协商后，协议栈就会触发这个事件
    case ESP_GAP_BLE_UPDATE_CONN_PARAMS_EVT:
        ESP_LOGI(BLE_GAP_TAG, "update connection params status = %d, conn_int = %d, latency = %d, timeout = %d",
                 param->update_conn_params.status,
                 param->update_conn_params.conn_int,
                 param->update_conn_params.latency,
                 param->update_conn_params.timeout);
        break;
    default:
        break;
    }
}

// GAP 层初始化
esp_err_t ble_gap_init(void)
{
    // 注册 GAP 事件回调函数，负责处理广播、连接、配对等通用蓝牙行为
    esp_err_t ret = esp_ble_gap_register_callback(gap_event_handler);
    if (ret != ESP_OK)
    {
        ESP_LOGE(BLE_GAP_TAG, "gap register error, error code = %x", ret);
        return ret;
    }

    ESP_LOGI(BLE_GAP_TAG, "GAP initialized successfully");
    return ESP_OK;
}

// 开始广播
esp_err_t ble_gap_start_advertising(void)
{
    return esp_ble_gap_start_advertising(&adv_params);
}

// 停止广播
esp_err_t ble_gap_stop_advertising(void)
{
    return esp_ble_gap_stop_advertising();
}

// 设置设备名称
esp_err_t ble_gap_set_device_name(const char *device_name)
{
    if (device_name == NULL)
    {
        ESP_LOGE(BLE_GAP_TAG, "Device name is NULL");
        return ESP_ERR_INVALID_ARG;
    }

    esp_err_t ret = esp_ble_gap_set_device_name(device_name);
    if (ret != ESP_OK)
    {
        ESP_LOGE(BLE_GAP_TAG, "set device name failed, error code = %x", ret);
        return ret;
    }

    ESP_LOGI(BLE_GAP_TAG, "Device name set to: %s", device_name);
    return ESP_OK;
}

// 配置广播数据
esp_err_t ble_gap_config_adv_data(void)
{
    esp_err_t ret;

#ifdef CONFIG_SET_RAW_ADV_DATA
    // 设置广播数据,完成后会触发ESP_GAP_BLE_ADV_DATA_RAW_SET_COMPLETE_EVT事件
    ret = esp_ble_gap_config_adv_data_raw(raw_adv_data, sizeof(raw_adv_data));
    if (ret != ESP_OK)
    {
        ESP_LOGE(BLE_GAP_TAG, "config raw adv data failed, error code = %x", ret);
        return ret;
    }
    adv_config_done |= ADV_CONFIG_FLAG;

    // 设置扫描应答数据,完成后触发ESP_GAP_BLE_SCAN_RSP_DATA_RAW_SET_COMPLETE_EVT事件
    ret = esp_ble_gap_config_scan_rsp_data_raw(raw_scan_rsp_data, sizeof(raw_scan_rsp_data));
    if (ret != ESP_OK)
    {
        ESP_LOGE(BLE_GAP_TAG, "config raw scan rsp data failed, error code = %x", ret);
        return ret;
    }
    adv_config_done |= SCAN_RSP_CONFIG_FLAG;
#else
    // 使用结构化广播数据
    ret = esp_ble_gap_config_adv_data(&adv_data);
    if (ret != ESP_OK)
    {
        ESP_LOGE(BLE_GAP_TAG, "config adv data failed, error code = %x", ret);
        return ret;
    }
    adv_config_done |= ADV_CONFIG_FLAG;

    ret = esp_ble_gap_config_adv_data(&scan_rsp_data);
    if (ret != ESP_OK)
    {
        ESP_LOGE(BLE_GAP_TAG, "config scan response data failed, error code = %x", ret);
        return ret;
    }
    adv_config_done |= SCAN_RSP_CONFIG_FLAG;
#endif

    ESP_LOGI(BLE_GAP_TAG, "Advertising data configured successfully");
    return ESP_OK;
}

// 获取广播配置完成状态
uint8_t ble_gap_get_adv_config_done(void)
{
    return adv_config_done;
}
